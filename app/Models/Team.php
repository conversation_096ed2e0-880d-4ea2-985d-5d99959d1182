<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Attributes\SetsNameAttribute;
use App\Models\Concerns\GetsForDropdown;
use App\Models\Concerns\Team\HandlesTeamRelationships;

/**
 * Team mode.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  int $leader_id
 * @property  bool $allows_general_ticket_visibility
 * @property  \DateTime $created_at
 * @property  \DateTime $updated_at
 *
 * @property  \App\Models\Operator $leader
 *
 * @property  \Illuminate\Support\Collection|\App\Models\TeamOperator[] $teamOperators
 */
class Team extends Model
{
    use HandlesTeamRelationships;
    use GetsForDropdown;
    use SetsNameAttribute;

    public const MODULE = Module::MANAGEMENT;
    public const RESOURCE_ROUTE = 'teams';

    protected $fillable = [
        'name',
        'leader_id',
        'allows_general_ticket_visibility',
    ];

    protected $casts = [
        'allows_general_ticket_visibility' => 'bool',
    ];
}
