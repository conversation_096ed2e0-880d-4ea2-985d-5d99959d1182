<?php

namespace App\Actions\Team;

use App\Models\Permission;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetTeam
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_TEAMS);
    }

    public function handle(Team $team): mixed
    {
        return $team->getBackEndActionGeneratorInstance()->loadShowView(compact('team'));
    }
}
