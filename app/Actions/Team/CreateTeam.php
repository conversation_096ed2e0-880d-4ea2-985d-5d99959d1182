<?php

namespace App\Actions\Team;

use App\Models\Permission;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateTeam
{
    use AsAction;

    protected array $rules = [
        'name' => 'required',
        'leader_id' => 'required',
        'allows_general_ticket_visibility' => 'required',
    ];

    protected array $messages = [
        'name.required' => 'É obrigatório informar o nome.',
        'leader_id.required' => 'É obrigatório informar o líder/gestor.'
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::CREATE_TEAMS);
    }

    public function asController(ActionRequest $request): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return Team::getBackendActionGenerator()->loadCreateView();
        }

        $request->merge(['allows_general_ticket_visibility' => $request->has('allows_general_ticket_visibility')]);

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $team = $this->handle($validator->validated());
            return redirect_success('teams.edit', __('teams.responses.create.success'), $team->id);
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $data): Team
    {
        try {
            return Team::create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
