<?php

namespace App\Actions\Team;

use App\Models\Team;
use Illuminate\Http\RedirectResponse;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteTeam
{
    use AsAction;

    public function asController(Team $team): RedirectResponse
    {
        try {
            $this->handle($team);
            return redirect_success('teams.index', __('teams.responses.delete.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(Team $team)
    {
        try {
            $team->delete();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
