<?php

namespace App\Actions\Team;

use App\Models\Permission;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditTeam
{
    use AsAction;

    protected array $rules = [
        'name' => 'required',
        'leader_id' => 'required',
        'allows_general_ticket_visibility' => 'required',
    ];

    protected array $messages = [
        'name.required' => 'É obrigatório informar o nome.',
        'leader_id.required' => 'É obrigatório informar o líder/gestor.'
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::UPDATE_TEAMS);
    }

    public function asController(ActionRequest $request, Team $team): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return Team::getBackendActionGenerator()->loadEditView(compact('team'));
        }

        $request->merge(['allows_general_ticket_visibility' => $request->has('allows_general_ticket_visibility')]);

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $this->handle($team, $validator->validated());
            return redirect_success('teams.index', __('teams.responses.update.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(Team $team, array $data): Team
    {
        try {
            $team->update($data);
            return $team;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
