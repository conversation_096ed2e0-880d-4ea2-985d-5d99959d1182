<?php

namespace App\Actions\Team;

use App\Core\Http\Requests\DeleteBatchRequest;
use App\Models\Permission;
use App\Models\Team;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Throwable;

class DeleteTeams
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::DELETE_TEAMS);
    }

    public function asController(DeleteBatchRequest $request): RedirectResponse
    {
        try {
            $this->handle($request->getDeleteIdsArray());
            return delete_batch_redirect_success('teams.index');
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $ids): void
    {
        try {
            collect($ids)->each(function (string $id): void {
                DeleteTeam::run(Team::find($id));
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
