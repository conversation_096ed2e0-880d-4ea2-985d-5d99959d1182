<?php

use App\Actions\Bank\GetBanksByCodeOrName;
use App\Actions\Category\CreateCategory;
use App\Actions\Category\DeleteCategories;
use App\Actions\Category\EditCategory;
use App\Actions\Category\ErpFlex\GetCategoriesFromErpFlex;
use App\Actions\Category\GetCategories;
use App\Actions\Category\GetCategory;
use App\Actions\Checklist\CreateChecklist;
use App\Actions\Checklist\DeleteChecklists;
use App\Actions\Checklist\EditChecklist;
use App\Actions\Checklist\GetChecklist;
use App\Actions\Checklist\GetChecklists;
use App\Actions\ChecklistPhase\CreateChecklistPhase;
use App\Actions\ChecklistPhase\DeleteChecklistPhases;
use App\Actions\ChecklistPhase\EditChecklistPhase;
use App\Actions\ChecklistPhase\GetChecklistPhase;
use App\Actions\ChecklistPhaseItem\CreateChecklistPhaseItem;
use App\Actions\ChecklistPhaseItem\DeleteChecklistPhaseItems;
use App\Actions\ChecklistPhaseItem\EditChecklistPhaseItem;
use App\Actions\ChecklistPhaseItem\GetChecklistPhaseItem;
use App\Actions\City\GetCitiesByName;
use App\Actions\City\GetCitiesByNameAndStateId;
use App\Actions\Cnae\GetCnae;
use App\Actions\Cnae\GetCnaes;
use App\Actions\CommissionRule\CreateCommissionRule;
use App\Actions\CommissionRule\DeleteCommissionRule;
use App\Actions\CommissionRule\EditCommissionRule;
use App\Actions\CommissionRule\GetCommissionRule;
use App\Actions\CommissionRule\GetCommissionRules;
use App\Actions\Company\ActivateCompanies;
use App\Actions\Company\ActivateCompany;
use App\Actions\Company\CreateCompany;
use App\Actions\Company\CreateLeadCompanyFromCompany;
use App\Actions\Company\DefineCompanyAdvanceNotice;
use App\Actions\Company\DeleteCompanies;
use App\Actions\Company\EditCompany;
use App\Actions\Company\ErpFlex\CreateCompanyInErpFlex;
use App\Actions\Company\GetBranchesByBranchNameOrTaxIdentificationNumber;
use App\Actions\Company\GetCompanies;
use App\Actions\Company\GetCompaniesByNameTradingNameOrTaxIdentificationNumber;
use App\Actions\Company\GetCompaniesFromErpFlex;
use App\Actions\Company\GetCompany;
use App\Actions\Company\GetCompanyBranches;
use App\Actions\Company\GetCompanyCnaeDetails;
use App\Actions\Company\GetCompanyLifeCount;
use App\Actions\Company\ImportCompanies;
use App\Actions\Company\InactivateCompanies;
use App\Actions\Company\InactivateCompany;
use App\Actions\Company\MarkCompanyAsCompliant;
use App\Actions\Company\MarkCompanyAsNormal;
use App\Actions\Company\MarkCompanyAsOverdue;
use App\Actions\Company\MarkCompanyAsPrime;
use App\Actions\Company\ReverseCompanyAdvanceNotice;
use App\Actions\Company\Soc\CreateCompanyInSoc;
use App\Actions\CompanyBranch\CreateCompanyBranch;
use App\Actions\CompanyBranch\DeleteCompanyBranch;
use App\Actions\CompanyContact\CreateCompanyContact;
use App\Actions\CompanyContact\DeleteCompanyContacts;
use App\Actions\CompanyContact\EditCompanyContact;
use App\Actions\CompanyContact\GetCompanyContact;
use App\Actions\CompanyFollowUp\CreateCompanyFollowUp;
use App\Actions\CompanyFollowUp\CreateCompanyFollowUpFromId;
use App\Actions\CompanyFollowUp\DeleteCompanyFollowUps;
use App\Actions\CompanyFollowUp\GetCompanyFollowUp;
use App\Actions\CompanyGroup\CreateCompanyGroup;
use App\Actions\CompanyGroup\DeleteCompanyGroups;
use App\Actions\CompanyGroup\EditCompanyGroup;
use App\Actions\CompanyGroup\GetCompanyGroup;
use App\Actions\CompanyGroup\GetCompanyGroups;
use App\Actions\CompanyGroupCompany\CreateCompanyGroupCompany;
use App\Actions\CompanyGroupCompany\DeleteCompanyGroupCompanies;
use App\Actions\CompanyGroupCompany\EditCompanyGroupCompany;
use App\Actions\CompanyGroupCompany\GetCompanyGroupCompany;
use App\Actions\CompanySocnetRelatedNameTerm\CreateCompanySocnetRelatedNameTerm;
use App\Actions\CompanySocnetRelatedNameTerm\DeleteCompanySocnetRelatedNameTerm;
use App\Actions\CompanySocnetRelatedNameTerm\EditCompanySocnetRelatedNameTerm;
use App\Actions\CompanySocnetRelatedNameTerm\GetCompanySocnetRelatedNameTerm;
use App\Actions\CompanySocnetRelatedNameTerm\GetCompanySocnetRelatedNameTerms;
use App\Actions\ExpenseType\CreateExpenseType;
use App\Actions\ExpenseType\DeleteExpenseTypes;
use App\Actions\ExpenseType\EditExpenseType;
use App\Actions\ExpenseType\GetExpenseType;
use App\Actions\ExpenseType\GetExpenseTypes;
use App\Actions\ExpenseType\GetExpenseTypesFromErpFlex;
use App\Actions\Holiday\CreateHoliday;
use App\Actions\Holiday\DeleteHoliday;
use App\Actions\Holiday\EditHoliday;
use App\Actions\Holiday\GetHoliday;
use App\Actions\Holiday\GetHolidays;
use App\Actions\Index\CreateIndex;
use App\Actions\Index\DeleteIndex;
use App\Actions\Index\EditIndex;
use App\Actions\Index\ExportIndices;
use App\Actions\Index\GetIndex;
use App\Actions\Index\GetIndexRevisions;
use App\Actions\Index\GetIndices;
use App\Actions\Index\GetIndicesByName;
use App\Actions\Operator\ActivateOperator;
use App\Actions\Operator\ActivateOperators;
use App\Actions\Operator\CreateOperator;
use App\Actions\Operator\DeleteOperators;
use App\Actions\Operator\EditOperator;
use App\Actions\Operator\GetAfterSalesSalesmenByNameOrEmail;
use App\Actions\Operator\GetOperator;
use App\Actions\Operator\GetOperators;
use App\Actions\Operator\GetOperatorsByNameOrEmail;
use App\Actions\Operator\GetSalesmenByNameOrEmail;
use App\Actions\Operator\InactivateOperator;
use App\Actions\Operator\InactivateOperators;
use App\Actions\PreCompany\ConvertPreCompanyToCompany;
use App\Actions\PreCompany\CreateExternalPreCompany;
use App\Actions\PreCompany\CreatePreCompany;
use App\Actions\PreCompany\DeletePreCompanies;
use App\Actions\PreCompany\EditPreCompany;
use App\Actions\PreCompany\GetPreCompanies;
use App\Actions\PreCompany\GetPreCompany;
use App\Actions\PreCompany\LoadSignUpSuccess;
use App\Actions\PreCompany\SendPreCompanySignUpLink;
use App\Actions\Procedure\CreateBatchEquivalences;
use App\Actions\Procedure\CreateProcedure;
use App\Actions\Procedure\DeleteEquivalence;
use App\Actions\Procedure\DeleteProcedures;
use App\Actions\Procedure\EditEquivalence;
use App\Actions\Procedure\EditProcedure;
use App\Actions\Procedure\ErpFlex\GetProceduresFromErpFlex;
use App\Actions\Procedure\GetActiveProceduresByName;
use App\Actions\Procedure\GetEquivalence;
use App\Actions\Procedure\GetEquivalences;
use App\Actions\Procedure\GetExamsPriceTable;
use App\Actions\Procedure\GetMainProceduresByName;
use App\Actions\Procedure\GetProcedure;
use App\Actions\Procedure\GetProcedureDataForCrm;
use App\Actions\Procedure\GetProcedures;
use App\Actions\Procedure\GetProceduresByName;
use App\Actions\Procedure\Soc\GetExamsFromSoc;
use App\Actions\ProcedureCrmCityCoverage\CreateProcedureCrmCityCoverage;
use App\Actions\ProcedureCrmCityCoverage\DeleteProcedureCrmCityCoverages;
use App\Actions\ProcedureCrmCityCoverage\EditProcedureCrmCityCoverage;
use App\Actions\ProcedureCrmCityCoverage\GetProcedureCrmCityCoverage;
use App\Actions\ProcedureCrmCityCoverageRange\CreateProcedureCrmCityCoverageRange;
use App\Actions\ProcedureCrmCityCoverageRange\DeleteProcedureCrmCityCoverageRanges;
use App\Actions\ProcedureCrmCityCoverageRange\EditProcedureCrmCityCoverageRange;
use App\Actions\ProcedureCrmCityCoverageRange\GetProcedureCrmCityCoverageRange;
use App\Actions\ProcedurePaymentMethod\CreateProcedurePaymentMethod;
use App\Actions\ProcedurePaymentMethod\DeleteProcedurePaymentMethods;
use App\Actions\ProcedurePaymentMethod\EditProcedurePaymentMethod;
use App\Actions\ProcedurePaymentMethod\GetProcedurePaymentMethod;
use App\Actions\ProcedurePaymentMethod\GetProcedurePaymentMethods;
use App\Actions\Reason\CreateReason;
use App\Actions\Reason\DeleteReasons;
use App\Actions\Reason\EditReason;
use App\Actions\Reason\GetReason;
use App\Actions\Reason\GetReasons;
use App\Actions\Role\CreateRole;
use App\Actions\Role\DeleteRole;
use App\Actions\Role\EditRole;
use App\Actions\Role\GetRole;
use App\Actions\Role\GetRoles;
use App\Actions\Schedule\CreateSchedule;
use App\Actions\Schedule\DeleteSchedule;
use App\Actions\Schedule\EditSchedule;
use App\Actions\Schedule\GetAvailableSchedules;
use App\Actions\Schedule\GetSchedule;
use App\Actions\Schedule\GetSchedules;
use App\Actions\Schedule\OpenScheduleForTechnician;
use App\Actions\ScheduleServiceOrder\GetScheduleServiceOrders;
use App\Actions\ServiceAmountRangeTemplateItem\CreateServiceAmountRangeTemplateItem;
use App\Actions\ServiceAmountRangeTemplateItem\DeleteServiceAmountRangeTemplateItem;
use App\Actions\ServiceAmountRangeTemplateItem\EditServiceAmountRangeTemplateItem;
use App\Actions\ServiceAmountRangeTemplateItem\GetServiceAmountRangeTemplateItem;
use App\Actions\ServiceAmountRangeTemplateItem\GetServiceAmountRangeTemplateItems;
use App\Actions\SocCompanyEnvironment\GetSocCompanyEnvironments;
use App\Actions\SocCompanyEnvironment\Soc\GetSocCompanyEnvironmentsFromSoc;
use App\Actions\State\GetStatesByAbbreviation;
use App\Actions\Subcategory\CreateSubcategory;
use App\Actions\Subcategory\DeleteSubcategories;
use App\Actions\Subcategory\EditSubcategory;
use App\Actions\Subcategory\GetSubcategory;
use App\Actions\Team\CreateTeam;
use App\Actions\Team\DeleteTeam;
use App\Actions\Team\DeleteTeams;
use App\Actions\Team\EditTeam;
use App\Actions\Team\GetTeam;
use App\Actions\Team\GetTeams;
use App\Actions\TeamOperator\CreateTeamOperator;
use App\Actions\TeamOperator\DeleteTeamOperators;
use App\Actions\TeamOperator\EditTeamOperator;
use App\Actions\TeamOperator\GetTeamOperator;
use App\Actions\TeamOperator\GetTeamOperators;
use App\Actions\Workflow\CreateWorkflow;
use App\Actions\Workflow\DeleteWorkflow;
use App\Actions\Workflow\EditWorkflow;
use App\Actions\Workflow\GetWorkflow;
use App\Actions\Workflow\GetWorkflows;
use Illuminate\Support\Facades\Route;

Route::prefix('states')->group(function () {
    Route::get('get-by-abbreviation', GetStatesByAbbreviation::class)->name('states.get_by_abbreviation');
});

Route::prefix('categories')->group(function () {
    Route::get('', GetCategories::class)->name('categories.index');
    Route::post('get-from-erp-flex', GetCategoriesFromErpFlex::class)->name('categories.get_from_erp_flex');
    Route::get('create', CreateCategory::class)->name('categories.create');
    Route::post('', CreateCategory::class)->name('categories.store');
    Route::delete('delete-batch', DeleteCategories::class)->name('categories.delete_batch');
    Route::prefix('{category}')->group(function () {
        Route::get('', GetCategory::class)->name('categories.show');
        Route::get('edit', EditCategory::class)->name('categories.edit');
        Route::put('', EditCategory::class)->name('categories.update');
        Route::prefix('subcategories')->group(function () {
            Route::get('create', CreateSubcategory::class)->name('subcategories.create');
            Route::post('', CreateSubcategory::class)->name('subcategories.store');
            Route::delete('delete-batch', DeleteSubcategories::class)->name('subcategories.delete_batch');
            Route::prefix('{subcategory}')->group(function () {
                Route::get('', GetSubcategory::class)->name('subcategories.show');
                Route::get('edit', EditSubcategory::class)->name('subcategories.edit');
                Route::put('', EditSubcategory::class)->name('subcategories.update');
            });
        });
    });
});

Route::prefix('checklists')->group(function () {
    Route::get('', GetChecklists::class)->name('checklists.index');
    Route::get('create', CreateChecklist::class)->name('checklists.create');
    Route::post('', CreateChecklist::class)->name('checklists.store');
    Route::delete('delete-batch', DeleteChecklists::class)->name('checklists.delete_batch');
    Route::prefix('{checklist}')->group(function () {
        Route::get('', GetChecklist::class)->name('checklists.show');
        Route::get('edit', EditChecklist::class)->name('checklists.edit');
        Route::put('', EditChecklist::class)->name('checklists.update');
        Route::prefix('phases')->group(function () {
            Route::get('create', CreateChecklistPhase::class)->name('checklist_phases.create');
            Route::post('', CreateChecklistPhase::class)->name('checklist_phases.store');
            Route::delete('delete-batch', DeleteChecklistPhases::class)->name('checklist_phases.delete_batch');
            Route::prefix('{checklist_phase}')->group(function () {
                Route::get('', GetChecklistPhase::class)->name('checklist_phases.show');
                Route::get('edit', EditChecklistPhase::class)->name('checklist_phases.edit');
                Route::put('', EditChecklistPhase::class)->name('checklist_phases.update');
                Route::prefix('items')->group(function () {
                    Route::get('create', CreateChecklistPhaseItem::class)->name('checklist_phase_items.create');
                    Route::post('', CreateChecklistPhaseItem::class)->name('checklist_phase_items.store');
                    Route::delete('delete-batch', DeleteChecklistPhaseItems::class)->name('checklist_phase_items.delete_batch');
                    Route::prefix('{checklist_phase_item}')->group(function () {
                        Route::get('', GetChecklistPhaseItem::class)->name('checklist_phase_items.show');
                        Route::get('edit', EditChecklistPhaseItem::class)->name('checklist_phase_items.edit');
                        Route::put('', EditChecklistPhaseItem::class)->name('checklist_phase_items.update');
                    });
                });
            });
        });
    });
});

Route::prefix('roles')
    ->name('roles.')
    ->group(function () {
        Route::get('', GetRoles::class)->name('index');
        Route::get('create', CreateRole::class)->name('create');
        Route::post('', CreateRole::class)->name('store');
        Route::get('{role}', GetRole::class)->name('show');
        Route::get('{role}/edit', EditRole::class)->name('edit');
        Route::put('{role}', EditRole::class)->name('update');
        Route::delete('{role}', DeleteRole::class)->name('destroy');
    });

Route::prefix('operators')
    ->name('operators.')
    ->group(function () {
        Route::get('', GetOperators::class)->name('index');
        Route::get('get-by-name-or-email', GetOperatorsByNameOrEmail::class)->name('get_by_name_or_email');
        Route::get('get-salesmen-by-name-or-email', GetSalesmenByNameOrEmail::class)->name('get_salesmen_by_name_or_email');
        Route::get('get-after-sales-salesmen-by-name-or-email', GetAfterSalesSalesmenByNameOrEmail::class)->name('get_after_sales_salesmen_by_name_or_email');
        Route::get('create', CreateOperator::class)->name('create');
        Route::post('', CreateOperator::class)->name('store');
        Route::post('activate', ActivateOperators::class)->name('activate_batch');
        Route::post('inactivate', InactivateOperators::class)->name('inactivate_batch');
        Route::delete('delete-batch', DeleteOperators::class)->name('delete_batch');
        Route::prefix('{operator}')->group(function () {
            Route::get('', GetOperator::class)->name('show');
            Route::get('edit', EditOperator::class)->name('edit');
            Route::post('inactivate', InactivateOperator::class)->name('inactivate');
            Route::post('activate', ActivateOperator::class)->name('activate');
            Route::put('', EditOperator::class)->name('update');
        });
    });

Route::prefix('holidays')
    ->name('holidays.')
    ->group(function () {
        Route::get('', GetHolidays::class)->name('index');
        Route::get('create', CreateHoliday::class)->name('create');
        Route::post('', CreateHoliday::class)->name('store');
        Route::prefix('{holiday}')->group(function () {
            Route::get('', GetHoliday::class)->name('show');
            Route::get('edit', EditHoliday::class)->name('edit');
            Route::put('', EditHoliday::class)->name('update');
            Route::delete('', DeleteHoliday::class)->name('destroy');
        });
    });

Route::prefix('indices')->group(function () {
    Route::get('', GetIndices::class)->name('indices.index');
    Route::get('get-by-name', GetIndicesByName::class)->name('indices.get_by_name');
    Route::get('create', CreateIndex::class)->name('indices.create');
    Route::post('export', ExportIndices::class)->name('indices.export');
    Route::post('', CreateIndex::class)->name('indices.store');
    Route::prefix('{index}')->group(function () {
        Route::get('', GetIndex::class)->name('indices.show');
        Route::get('edit', EditIndex::class)->name('indices.edit');
        Route::put('', EditIndex::class)->name('indices.update');
        Route::delete('', DeleteIndex::class)->name('indices.destroy');
        Route::prefix('revisions')->group(function () {
            Route::get('', GetIndexRevisions::class)->name('index_revisions.index');
        });
    });
});

Route::prefix('procedures')->group(function () {
    Route::get('', GetProcedures::class)->name('procedures.index');
    Route::post('get-data-for-crm', GetProcedureDataForCrm::class)->name('procedures.get_data_for_crm');
    Route::post('get-from-erp-flex', GetProceduresFromErpFlex::class)->name('procedures.get_from_erp_flex');
    Route::post('get-exams-from-soc', GetExamsFromSoc::class)->name('procedures.get_exams_from_soc');
    Route::get('get-by-name', GetProceduresByName::class)->name('procedures.get_by_name');
    Route::get('get-active-by-name', GetActiveProceduresByName::class)->name('procedures.get_active_by_name');
    Route::get('get-main-by-name', GetMainProceduresByName::class)->name('procedures.get_main_by_name');
    Route::get('equivalences', GetEquivalences::class)->name('procedures.equivalences');
    Route::get('exams-price-table', GetExamsPriceTable::class)->name('procedures.get_exams_price_table');
    Route::get('create', CreateProcedure::class)->name('procedures.create');
    Route::post('store', CreateProcedure::class)->name('procedures.store');
    Route::get('create-batch-equivalences', CreateBatchEquivalences::class)->name('procedures.create_batch_equivalences');
    Route::post('create-batch-equivalences', CreateBatchEquivalences::class)->name('procedures.store_batch_equivalences');
    Route::delete('delete-batch', DeleteProcedures::class)->name('procedures.delete_batch');
    Route::prefix('{procedure}')->group(function () {
        Route::get('', GetProcedure::class)->name('procedures.show');
        Route::get('show-equivalence', GetEquivalence::class)->name('procedures.show_equivalence');
        Route::get('edit', EditProcedure::class)->name('procedures.edit');
        Route::get('edit-equivalence', EditEquivalence::class)->name('procedures.edit_equivalence');
        Route::put('', EditProcedure::class)->name('procedures.update');
        Route::put('equivalence', EditEquivalence::class)->name('procedures.update_equivalence');
        Route::delete('equivalence', DeleteEquivalence::class)->name('procedures.destroy_equivalence');
        Route::prefix('crm-city-coverages')->group(function () {
            Route::get('create', CreateProcedureCrmCityCoverage::class)->name('procedure_crm_city_coverages.create');
            Route::post('', CreateProcedureCrmCityCoverage::class)->name('procedure_crm_city_coverages.store');
            Route::delete('delete-batch', DeleteProcedureCrmCityCoverages::class)->name('procedure_crm_city_coverages.delete_batch');
            Route::prefix('{procedure_crm_city_coverage}')->group(function () {
                Route::get('', GetProcedureCrmCityCoverage::class)->name('procedure_crm_city_coverages.show');
                Route::get('edit', EditProcedureCrmCityCoverage::class)->name('procedure_crm_city_coverages.edit');
                Route::put('', EditProcedureCrmCityCoverage::class)->name('procedure_crm_city_coverages.update');
                Route::prefix('range')->group(function () {
                    Route::get('create', CreateProcedureCrmCityCoverageRange::class)->name('procedure_crm_city_coverage_ranges.create');
                    Route::post('', CreateProcedureCrmCityCoverageRange::class)->name('procedure_crm_city_coverage_ranges.store');
                    Route::delete('delete-batch', DeleteProcedureCrmCityCoverageRanges::class)->name('procedure_crm_city_coverage_ranges.delete_batch');
                    Route::prefix('{range}')->group(function () {
                        Route::get('', GetProcedureCrmCityCoverageRange::class)->name('procedure_crm_city_coverage_ranges.show');
                        Route::get('edit', EditProcedureCrmCityCoverageRange::class)->name('procedure_crm_city_coverage_ranges.edit');
                        Route::put('', EditProcedureCrmCityCoverageRange::class)->name('procedure_crm_city_coverage_ranges.update');
                    });
                });
            });
        });
        Route::prefix('amount-range-template-items')->group(function () {
            Route::get('', GetServiceAmountRangeTemplateItems::class)->name('service_amount_range_template_items.index');
            Route::get('create', CreateServiceAmountRangeTemplateItem::class)->name('service_amount_range_template_items.create');
            Route::post('', CreateServiceAmountRangeTemplateItem::class)->name('service_amount_range_template_items.store');
            Route::prefix('{amount_range_template_item}')->group(function () {
                Route::get('', GetServiceAmountRangeTemplateItem::class)->name('service_amount_range_template_items.show');
                Route::get('edit', EditServiceAmountRangeTemplateItem::class)->name('service_amount_range_template_items.edit');
                Route::put('', EditServiceAmountRangeTemplateItem::class)->name('service_amount_range_template_items.update');
                Route::delete('', DeleteServiceAmountRangeTemplateItem::class)->name('service_amount_range_template_items.destroy');
            });
        });
    });
});

Route::prefix('expense-types')
    ->name('expense_types.')
    ->group(function () {
        Route::get('', GetExpenseTypes::class)->name('index');
        Route::post('get-from-erp-flex', GetExpenseTypesFromErpFlex::class)->name('get_from_erp_flex');
        Route::get('create', CreateExpenseType::class)->name('create');
        Route::post('', CreateExpenseType::class)->name('store');
        Route::delete('delete-batch', DeleteExpenseTypes::class)->name('delete_batch');
        Route::prefix('{expense_type}')->group(function () {
            Route::get('', GetExpenseType::class)->name('show');
            Route::get('edit', EditExpenseType::class)->name('edit');
            Route::put('', EditExpenseType::class)->name('update');
        });
    });

Route::prefix('companies')->group(function () {
    Route::get('', GetCompanies::class)->name('companies.index');
    Route::get('get-by-name-trading-name-or-tax-id-number', GetCompaniesByNameTradingNameOrTaxIdentificationNumber::class)->name('companies.get_by_name_trading_name_or_tax_id_number');
    Route::get('get-branches-by-branch-name-or-tax-id-number', GetBranchesByBranchNameOrTaxIdentificationNumber::class)->name('companies.get_branches_by_branch_name_or_tax_id_number');
    Route::post('get-from-erp-flex', GetCompaniesFromErpFlex::class)->name('companies.get_from_erp_flex');
    Route::get('create', CreateCompany::class)->name('companies.create');
    Route::post('', CreateCompany::class)->name('companies.store');
    Route::post('import', ImportCompanies::class)->name('companies.import');
    Route::post('activate', ActivateCompanies::class)->name('companies.activate_batch');
    Route::post('inactivate', InactivateCompanies::class)->name('companies.inactivate_batch');
    Route::delete('delete-batch', DeleteCompanies::class)->name('companies.delete_batch');
    Route::prefix('{company}')->group(function () {
        Route::get('', GetCompany::class)->name('companies.show');
        Route::get('edit', EditCompany::class)->name('companies.edit');
        Route::post('activate', ActivateCompany::class)->name('companies.activate');
        Route::post('inactivate', InactivateCompany::class)->name('companies.inactivate');
        Route::post('define-advance-notice', DefineCompanyAdvanceNotice::class)->name('companies.define_advance_notice');
        Route::post('reverse-advance-notice', ReverseCompanyAdvanceNotice::class)->name('companies.reverse_advance_notice');
        Route::post('mark-as-compliant', MarkCompanyAsCompliant::class)->name('companies.mark_as_compliant');
        Route::post('mark-as-overdue', MarkCompanyAsOverdue::class)->name('companies.mark_as_overdue');
        Route::post('create-in-erp-flex', CreateCompanyInErpFlex::class)->name('companies.create_in_erp_flex');
        Route::post('create-in-soc', CreateCompanyInSoc::class)->name('companies.create_in_soc');
        Route::post('create-in-crm-as-lead-company', CreateLeadCompanyFromCompany::class)->name('companies.create_in_crm_as_lead_company');
        Route::post('mark-as-prime', MarkCompanyAsPrime::class)->name('companies.mark_as_prime');
        Route::post('mark-as-normal', MarkCompanyAsNormal::class)->name('companies.mark_as_normal');
        Route::get('get-life-count', GetCompanyLifeCount::class)->name('companies.get_life_count');
        Route::get('get-cnae-details', GetCompanyCnaeDetails::class)->name('companies.get_cnae_details');
        Route::put('', EditCompany::class)->name('companies.update');
        Route::prefix('contacts')->group(function () {
            Route::get('create', CreateCompanyContact::class)->name('company_contacts.create');
            Route::post('', CreateCompanyContact::class)->name('company_contacts.store');
            Route::delete('delete-batch', DeleteCompanyContacts::class)->name('company_contacts.delete_batch');
            Route::prefix('{company_contact}')->group(function () {
                Route::get('', GetCompanyContact::class)->name('company_contacts.show');
                Route::get('edit', EditCompanyContact::class)->name('company_contacts.edit');
                Route::put('', EditCompanyContact::class)->name('company_contacts.update');
            });
        });
        Route::prefix('branches')->group(function () {
            Route::get('', GetCompanyBranches::class)->name('company_branches.index');
            Route::get('create', CreateCompanyBranch::class)->name('company_branches.create');
            Route::post('', CreateCompanyBranch::class)->name('company_branches.store');
            Route::prefix('{branch}')->group(function () {
                Route::delete('', DeleteCompanyBranch::class)->name('company_branches.destroy');
            });
        });
        Route::prefix('socnet-related-name-terms')->group(function () {
            Route::get('', GetCompanySocnetRelatedNameTerms::class)->name('company_socnet_related_name_terms.index');
            Route::get('create', CreateCompanySocnetRelatedNameTerm::class)->name('company_socnet_related_name_terms.create');
            Route::post('', CreateCompanySocnetRelatedNameTerm::class)->name('company_socnet_related_name_terms.store');
            Route::prefix('{company_socnet_related_name_term}')->group(function () {
                Route::get('', GetCompanySocnetRelatedNameTerm::class)->name('company_socnet_related_name_terms.show');
                Route::get('edit', EditCompanySocnetRelatedNameTerm::class)->name('company_socnet_related_name_terms.edit');
                Route::put('', EditCompanySocnetRelatedNameTerm::class)->name('company_socnet_related_name_terms.update');
                Route::delete('', DeleteCompanySocnetRelatedNameTerm::class)->name('company_socnet_related_name_terms.destroy');
            });
        });
        Route::prefix('follow-ups')->group(function () {
            Route::get('create', CreateCompanyFollowUp::class)->name('company_follow_ups.create');
            Route::post('', CreateCompanyFollowUp::class)->name('company_follow_ups.store');
            Route::delete('delete-batch', DeleteCompanyFollowUps::class)->name('company_follow_ups.delete_batch');
            Route::prefix('{company_follow_up}')->group(function () {
                Route::get('', GetCompanyFollowUp::class)->name('company_follow_ups.show');
                Route::get('from-id', CreateCompanyFollowUpFromId::class)->name('company_follow_ups.create_from_id');
                Route::post('from-id', CreateCompanyFollowUpFromId::class)->name('company_follow_ups.store_from_id');
            });
        });
    });
});

Route::prefix('cnaes')
    ->name('cnaes.')
    ->group(function () {
        Route::get('', GetCnaes::class)->name('index');
        Route::get('{cnae}', GetCnae::class)->name('show');
    });

Route::prefix('cities')
    ->name('cities.')
    ->group(function () {
        Route::get('get-by-name', GetCitiesByName::class)->name('get_by_name');
        Route::get('get-by-name-and-state-id', GetCitiesByNameAndStateId::class)->name('get_by_name_and_state_id');
    });

Route::prefix('workflows')
    ->name('workflows.')
    ->group(function () {
        Route::get('', GetWorkflows::class)->name('index');
        Route::get('create', CreateWorkflow::class)->name('create');
        Route::post('', CreateWorkflow::class)->name('store');
        Route::prefix('{workflow}')->group(function () {
            Route::get('', GetWorkflow::class)->name('show');
            Route::get('edit', EditWorkflow::class)->name('edit');
            Route::put('', EditWorkflow::class)->name('update');
            Route::delete('', DeleteWorkflow::class)->name('destroy');
        });
    });

Route::prefix('schedules')
    ->name('schedules.')
    ->group(function () {
        Route::get('', GetSchedules::class)->name('index');
        Route::get('get-available-schedules', GetAvailableSchedules::class)->name('get_available_schedules');
        Route::get('create', CreateSchedule::class)->name('create');
        Route::post('', CreateSchedule::class)->name('store');
        Route::post('open-schedule', OpenScheduleForTechnician::class)->name('open_schedule');
        Route::prefix('{schedule}')->group(function () {
            Route::get('', GetSchedule::class)->name('show');
            Route::get('get-service-orders', GetScheduleServiceOrders::class)->name('get_service_orders');
            Route::get('edit', EditSchedule::class)->name('edit');
            Route::put('', EditSchedule::class)->name('update');
            Route::delete('', DeleteSchedule::class)->name('destroy');
        });
    });

Route::prefix('commission-rules')
    ->name('commission_rules.')
    ->group(function () {
        Route::get('', GetCommissionRules::class)->name('index');
        Route::get('create', CreateCommissionRule::class)->name('create');
        Route::post('', CreateCommissionRule::class)->name('store');
        Route::prefix('{commission_rule}')->group(function () {
            Route::get('', GetCommissionRule::class)->name('show');
            Route::get('edit', EditCommissionRule::class)->name('edit');
            Route::put('', EditCommissionRule::class)->name('update');
            Route::delete('', DeleteCommissionRule::class)->name('destroy');
        });
    });

Route::prefix('banks')
    ->name('banks.')
    ->group(function () {
        Route::get('get-by-code-or-name', GetBanksByCodeOrName::class)->name('get_by_code_or_name');
    });

Route::prefix('pre-companies')
    ->name('pre_companies.')
    ->group(function () {
        Route::get('', GetPreCompanies::class)->name('index');
        Route::get('create', CreatePreCompany::class)->name('create');
        Route::get('create-external/{token}', CreateExternalPreCompany::class)->name('create_external');
        Route::post('send-sign-up-link', SendPreCompanySignUpLink::class)->name('send_sign_up_link');
        Route::get('load-sign-up-success', LoadSignUpSuccess::class)->name('load_sign_up_success');
        Route::post('', CreatePreCompany::class)->name('store');
        Route::delete('delete-batch', DeletePreCompanies::class)->name('delete_batch');
        Route::prefix('{pre_company}')->group(function () {
            Route::get('', GetPreCompany::class)->name('show');
            Route::get('edit', EditPreCompany::class)->name('edit');
            Route::post('convert-to-company', ConvertPreCompanyToCompany::class)->name('convert_to_company');
            Route::put('', EditPreCompany::class)->name('update');
        });
    });

Route::prefix('teams')->group(function () {
    Route::get('', GetTeams::class)->name('teams.index');
    Route::get('create', CreateTeam::class)->name('teams.create');
    Route::post('', CreateTeam::class)->name('teams.store');
    Route::delete('delete-batch', DeleteTeams::class)->name('teams.delete_batch');
    Route::prefix('{team}')->group(function () {
        Route::get('', GetTeam::class)->name('teams.show');
        Route::get('edit', EditTeam::class)->name('teams.edit');
        Route::put('', EditTeam::class)->name('teams.update');
        Route::prefix('operators')->group(function () {
            Route::get('', GetTeamOperators::class)->name('team_operators.index');
            Route::get('create', CreateTeamOperator::class)->name('team_operators.create');
            Route::post('', CreateTeamOperator::class)->name('team_operators.store');
            Route::delete('delete-batch', DeleteTeamOperators::class)->name('team_operators.delete_batch');
            Route::prefix('{team_operator}')->group(function () {
                Route::get('', GetTeamOperator::class)->name('team_operators.show');
                Route::get('edit', EditTeamOperator::class)->name('team_operators.edit');
                Route::put('', EditTeamOperator::class)->name('team_operators.update');
            });
        });
    });
});

Route::prefix('soc-company-environments')->group(function () {
    Route::get('', GetSocCompanyEnvironments::class)->name('soc_company_environments.index');
    Route::post('get-from-soc', GetSocCompanyEnvironmentsFromSoc::class)->name('soc_company_environments.get_from_soc');
});

Route::prefix('company-groups')->group(function () {
    Route::get('', GetCompanyGroups::class)->name('company_groups.index');
    Route::get('create', CreateCompanyGroup::class)->name('company_groups.create');
    Route::post('', CreateCompanyGroup::class)->name('company_groups.store');
    Route::delete('delete-batch', DeleteCompanyGroups::class)->name('company_groups.delete_batch');
    Route::prefix('{company_group}')->group(function () {
        Route::get('', GetCompanyGroup::class)->name('company_groups.show');
        Route::get('edit', EditCompanyGroup::class)->name('company_groups.edit');
        Route::put('', EditCompanyGroup::class)->name('company_groups.update');
        Route::prefix('companies')->group(function () {
            Route::get('create', CreateCompanyGroupCompany::class)->name('company_group_companies.create');
            Route::post('', CreateCompanyGroupCompany::class)->name('company_group_companies.store');
            Route::delete('delete-batch', DeleteCompanyGroupCompanies::class)->name('company_group_companies.delete_batch');
            Route::prefix('{company_group_company}')->group(function () {
                Route::get('', GetCompanyGroupCompany::class)->name('company_group_companies.show');
                Route::get('edit', EditCompanyGroupCompany::class)->name('company_group_companies.edit');
                Route::put('', EditCompanyGroupCompany::class)->name('company_group_companies.update');
            });
        });
    });
});


Route::prefix('procedure-payment-methods')->group(function () {
    Route::get('', GetProcedurePaymentMethods::class)->name('procedure_payment_methods.index');
    Route::get('create', CreateProcedurePaymentMethod::class)->name('procedure_payment_methods.create');
    Route::post('', CreateProcedurePaymentMethod::class)->name('procedure_payment_methods.store');
    Route::delete('delete-batch', DeleteProcedurePaymentMethods::class)->name('procedure_payment_methods.delete_batch');
    Route::prefix('{procedure_payment_method}')->group(function () {
        Route::get('', GetProcedurePaymentMethod::class)->name('procedure_payment_methods.show');
        Route::get('edit', EditProcedurePaymentMethod::class)->name('procedure_payment_methods.edit');
        Route::put('', EditProcedurePaymentMethod::class)->name('procedure_payment_methods.update');
    });
});

Route::prefix('reasons')->group(function () {
    Route::get('', GetReasons::class)->name('reasons.index');
    Route::get('create', CreateReason::class)->name('reasons.create');
    Route::post('', CreateReason::class)->name('reasons.store');
    Route::delete('delete-batch', DeleteReasons::class)->name('reasons.delete_batch');
    Route::prefix('{reason}')->group(function () {
        Route::get('', GetReason::class)->name('reasons.show');
        Route::get('edit', EditReason::class)->name('reasons.edit');
        Route::put('', EditReason::class)->name('reasons.update');
    });
});
