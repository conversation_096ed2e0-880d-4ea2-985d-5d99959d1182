@extends('layouts.app')

@section('content')
    <x-card :title="__('teams.cards.create.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('teams.index')">
        <x-form.create-form :$resourceRoute>
            <x-tabs id="teams-create-tabs">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="general" text="Geral" :active="true" />
                    <x-tabs.tab-header-item id="tickets" text="Chamados" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" required="true" />
                                <x-input.select md="6" lg="6" :$resourceRoute :$action field="leader_id" required="true" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="tickets">
                        <x-form.section>
                            <x-form.row>
                                <x-input.checkbox :$resourceRoute :$action field="allows_general_ticket_visibility" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.create-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script>
        $(document).ready(() => {
            initializeSelect2('leader_id', "{{ route('operators.get_by_name_or_email') }}", 'Digite o nome ou o e-mail de um operador');
        });
    </script>
@endsection
