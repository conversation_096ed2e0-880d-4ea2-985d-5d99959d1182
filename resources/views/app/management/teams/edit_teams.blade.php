@extends('layouts.app')

@section('content')
    <x-card :title="__('teams.cards.edit.header.title')" :cardHeaderTitle="true" :backButton="true" :backRoute="route('teams.index')">
        <x-form.edit-form :$resourceRoute :routeParameters="$team->id">
            <x-tabs id="teams-edit-tabs">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="general" text="Geral" :active="true" />
                    <x-tabs.tab-header-item id="tickets" text="Chamados" />
                    <x-tabs.tab-header-item id="team-operators" text="Operadores" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="name" required="true" value="{{ $team->name }}" />
                                <x-input.select md="6" lg="6" :$resourceRoute :$action field="leader_id" required="true">
                                    <option value="{{ $team->leader_id }}">{{ $team->leader->name }}</option>
                                </x-input.select>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="tickets">
                        <x-form.section>
                            <x-form.row>
                                <x-input.checkbox :$resourceRoute :$action field="allows_general_ticket_visibility" :checked="$team->allows_general_ticket_visibility" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    <x-tabs.tab-content-item id="team-operators">
                        <x-form.section :marginTop="true" :createsResource="true" :createResourceRoute="route('team_operators.create', $team->id)">
                            <livewire:team-operator.tables.team-operators-table :teamId="$team->id" />
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>
@endsection

@section('js-scripts')
    <script src="{{ global_asset('js/core/components/select2/select2_helpers.js') }}"></script>
    <script>
        $(document).ready(() => {
            initializeSelect2('leader_id', "{{ route('operators.get_by_name_or_email') }}", 'Digite o nome ou o e-mail de um operador');
        });
    </script>
@endsection
